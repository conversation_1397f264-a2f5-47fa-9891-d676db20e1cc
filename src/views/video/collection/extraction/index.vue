<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 自定义视频文件选择字段 -->
    <template #form:value1:simple="{ model, field }">
      <VideoFileSelector
        v-model="model[field]"
        @change="handleVideoFileChange"
      />
    </template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after="{ selected, selection }">

    </template>

    <!-- 萃取状态列自定义渲染 -->
    <template #table:value9:simple="{ row }">
      <el-tag :type="getStatusTagType(row.value9)">
        {{ row.value9 }}
      </el-tag>
    </template>

    <!-- 文件格式列自定义渲染 -->
    <template #table:value11:simple="{ row }">
      <el-tag type="info">
        {{ row.value11 }}
      </el-tag>
    </template>

    <!-- 剔除黑屏列自定义渲染 -->
    <template #table:value5:simple="{ row }">
      <el-tag :type="row.value5 === '是' ? 'success' : 'info'">
        {{ row.value5 }}
      </el-tag>
    </template>

    <!-- 剔除花屏列自定义渲染 -->
    <template #table:value6:simple="{ row }">
      <el-tag :type="row.value6 === '是' ? 'success' : 'info'">
        {{ row.value6 }}
      </el-tag>
    </template>

    <template #table:action:after="{ row }">
      <el-button
        v-if="['待执行', '已停止'].includes(row.value9)"
        type="text"
        size="mini"
        @click="handleExecute(row)"
      >
        执行
      </el-button>
      <el-button
        v-if="['运行中', '进行中'].includes(row.value9)"
        type="text"
        size="mini"
        @click="handleStop(row)"
      >
        停止
      </el-button>
      <el-button
        v-if="['异常', '已完成'].includes(row.value9)"
        type="text"
        size="mini"
        @click="handleReExecute(row)"
      >
        重新执行
      </el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { fileFormat, fileStatus, taskStatus } from '@/dicts/video/index.js'
import VideoFileSelector from '@/components/VideoFileSelector/index.vue'

export default {
  name: 'VideoExtractionManagement',
  components: {
    VideoFileSelector
  },
  data() {
    return {
      tableType: 'value_extraction_detection'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频萃取管理',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 视频文件选择
          value1: {
            type: 'text',
            label: '视频唯一编号',
            align: 'left',
            width: 200,
            search: {
              hidden: true
            },
            form: {
              label: '视频文件',
              lg: 24,
              rules: [
                { required: true, message: '请选择视频文件', trigger: 'change' }
              ]
            }
          },
          // 任务编号
          value2: {
            type: 'text',
            label: '任务编号',
            align: 'left',
            width: 150,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },

          // 视频文件名
          value3: {
            type: 'text',
            label: '视频文件名',
            width: 200,
            search: {
              placeholder: '请输入文件名'
            },
            form: {
              hidden: true
            }
          },
          // 文件大小
          value4: {
            type: 'text',
            label: '文件大小',
            width: 120,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          // 剔除黑屏
          value5: {
            type: 'select',
            label: '剔除黑屏',
            width: 100,
            search: {
              hidden: true
            },
            form: {
              value: '是'
            },
            options: [
              {
                label: '是',
                value: '是'
              },
              {
                label: '否',
                value: '否'
              }
            ]
          },
          // 剔除花屏
          value6: {
            type: 'select',
            label: '剔除花屏',
            width: 100,
            search: {
              hidden: true
            },
            form: {
              value: '是'
            },
            options: [
              {
                label: '是',
                value: '是'
              },
              {
                label: '否',
                value: '否'
              }
            ]
          },
          // 剔除后视频文件名
          value7: {
            type: 'text',
            label: '剔除后视频文件名',
            width: 200,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          // 萃取时间
          value8: {
            type: 'text',
            label: '萃取时间',
            width: 160,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          // 萃取状态
          value9: {
            type: 'select',
            label: '萃取状态',
            width: 100,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...taskStatus
              ]
            },
            add: {
              hidden: true,
              value: '待执行'
            },
            table: {
              tableColumnProps: {
                fixed: 'right'
              }
            },
            options: taskStatus
          },
          // 失败原因
          value10: {
            type: 'text',
            label: '失败原因',
            width: 200,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          // 文件格式（搜索用）
          value11: {
            type: 'select',
            label: '文件格式',
            width: 100,
            hidden: true,
            search: {
              hidden: false,
              type: 'select',
              options: [
                { label: '全部格式', value: '' },
                ...fileFormat
              ]
            },
            options: fileFormat
          },
          // 文件状态（搜索用）
          value12: {
            type: 'select',
            label: '文件状态',
            width: 100,
            hidden: true,
            search: {
              hidden: false,
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...fileStatus
              ]
            },
            options: fileStatus
          }
        }
      }
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '运行中': 'success',
        '已完成': 'success',
        '已停止': 'info',
        '待执行': 'warning',
        '异常': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 执行任务
    async handleExecute(row) {
      try {
        await this.$modal.confirm(`确认要执行萃取任务吗？`)

        const updateData = {
          ...row,
          value9: '运行中',
          action: '执行'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已开始执行')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('执行任务失败:', error)
          this.$modal.msgError('执行任务失败')
        }
      }
    },

    // 停止任务
    async handleStop(row) {
      try {
        await this.$modal.confirm(`确认要停止萃取任务吗？`)

        const updateData = {
          ...row,
          value9: '已停止',
          action: '停止'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已停止')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('停止任务失败:', error)
          this.$modal.msgError('停止任务失败')
        }
      }
    },

    // 重新执行任务
    async handleReExecute(row) {
      try {
        await this.$modal.confirm(`确认要重新执行萃取任务吗？`)

        const updateData = {
          ...row,
          value9: '运行中',
          action: '重新'
        }

        await this.sheetProps.api.edit(updateData)
        this.$modal.msgSuccess('任务已重新执行')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重新执行任务失败:', error)
          this.$modal.msgError('重新执行任务失败')
        }
      }
    },

    // 调用重新萃取接口
    async reExtractionVideos(ids) {
      try {
        await request({
          url: '/system/AutoOsmotic/reExtraction',
          method: 'post',
          data: {
            ids: ids,
            type: this.tableType
          }
        })
        this.$modal.msgSuccess('重新萃取任务已提交')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        this.$modal.msgError('重新萃取失败：' + (error.message || '未知错误'))
      }
    },

    // 视频文件选择变化处理
    handleVideoFileChange(fileId, fileInfo) {
      if (fileInfo) {
        console.log('选择的视频文件:', fileId, fileInfo)
        // 可以在这里处理文件选择后的逻辑，比如自动填充其他字段
        // 例如：自动填充文件名到 value3 字段
        // this.$refs.sheetRef.formMixin.data.value3 = fileInfo.value3
      }
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
