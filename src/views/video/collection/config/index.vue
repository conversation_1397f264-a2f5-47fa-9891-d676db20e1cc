<template>
  <div class="config-container p-0 bg-gray-50">
    <div class="mx-auto">
      <!-- 配置保存 -->
      <div class="mb-6 flex items-center justify-between bg-white p-4 rounded-md shadow-el sticky top-16 z-100">
        <div class="flex items-center space-x-4">
          <el-checkbox v-model="saveAsDefault" @change="onSaveOptionChange">
            保存为默认配置
          </el-checkbox>
          <el-tooltip content="保存为默认配置后，新建任务时将自动应用这些设置" placement="top">
            <i class="el-icon-info text-gray-400 cursor-help"></i>
          </el-tooltip>
        </div>

        <div class="flex items-center space-x-3">
          <el-button @click="resetConfig">重置</el-button>
          <el-button type="info" @click="previewConfig">预览配置</el-button>
          <el-button
            type="primary"
            :loading="saving"
            @click="saveConfig"
          >
            {{ saving ? '保存中...' : '保存配置' }}
          </el-button>
        </div>
      </div>

      <!-- 配置标签页 -->
      <el-card class="mb-6 shadow-el">
        <el-tabs v-model="activeTab" type="card" class="config-tabs">
          <!-- 萃取规则标签页 -->
          <el-tab-pane label="萃取规则" name="extraction">
            <!-- 黑屏/花屏检测配置 -->
            <div class="config-block mt-1">
              <div class="config-block-header">
                <i class="el-icon-warning text-orange-500 mr-2"></i>
                <span class="text-lg font-semibold">黑屏 / 花屏检测</span>
                <el-tooltip content="检测视频中的黑屏和花屏异常" placement="top">
                  <i class="el-icon-question text-gray-400 ml-2 cursor-help"></i>
                </el-tooltip>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 黑屏判定阈值 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    黑屏判定阈值
                    <span class="text-red-500">*</span>
                    <el-tooltip content="图像亮度阈值，0-255，建议值：10-30" placement="top">
                      <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
                    </el-tooltip>
                  </label>
                  <div class="flex items-center space-x-4">
                    <div class="text-xs">亮度</div>
                    <el-slider
                      v-model="abnormalConfig.blackScreenThreshold"
                      :min="0"
                      :max="255"
                      :step="1"
                      :show-tooltip="true"
                      class="flex-1"
                      @change="onAbnormalChange"
                    />
                    <el-input-number
                      v-model="abnormalConfig.blackScreenThreshold"
                      :min="0"
                      :max="255"
                      :step="1"
                      size="small"
                      class="w-20"
                      @change="onAbnormalChange"
                    />
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    建议值：10-30
                  </div>
                </div>

                <!-- 花屏判定阈值 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    花屏判定阈值
                    <span class="text-red-500">*</span>
                    <el-tooltip content="图像异常比例，0-1之间，建议值：0.3-0.7" placement="top">
                      <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
                    </el-tooltip>
                  </label>
                  <div class="flex items-center space-x-4">
                    <div class="text-xs">图像异常比例</div>
                    <el-slider
                      v-model="abnormalConfig.noiseThreshold"
                      :min="0"
                      :max="1"
                      :step="0.01"
                      :show-tooltip="true"
                      class="flex-1"
                      @change="onAbnormalChange"
                    />
                    <el-input-number
                      v-model="abnormalConfig.noiseThreshold"
                      :min="0"
                      :max="1"
                      :step="0.01"
                      :precision="2"
                      size="small"
                      class="w-24"
                      @change="onAbnormalChange"
                    />
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    建议值：0.3-0.7
                  </div>
                </div>

                <!-- 最小异常持续时间 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    最小异常持续时间
                    <span class="text-red-500">*</span>
                    <el-tooltip content="异常状态持续时间，单位：秒，建议值：1-5秒" placement="top">
                      <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
                    </el-tooltip>
                  </label>
                  <div class="flex items-center space-x-4">
                    <el-slider
                      v-model="abnormalConfig.minDuration"
                      :min="0.1"
                      :max="10"
                      :step="0.1"
                      :show-tooltip="true"
                      class="flex-1"
                      @change="onAbnormalChange"
                    />
                    <el-input-number
                      v-model="abnormalConfig.minDuration"
                      :min="0.1"
                      :max="10"
                      :step="0.1"
                      :precision="1"
                      size="small"
                      class="w-20"
                      @change="onAbnormalChange"
                    />
                    <span class="text-sm text-gray-500">秒</span>
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    建议值：1-5秒
                  </div>
                </div>
              </div>

              <!-- 处理策略 -->
              <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">
                  处理策略
                  <span class="text-red-500">*</span>
                </label>
                <el-radio-group v-model="abnormalConfig.strategy" @change="onAbnormalChange">
                  <el-radio label="delete" class="mb-2">
                    <span class="flex items-center">
                      <i class="el-icon-delete text-red-500 mr-2"></i>
                      删除异常帧
                      <span class="text-xs text-gray-500 ml-2">直接删除检测到的异常帧</span>
                    </span>
                  </el-radio>
                  <el-radio label="replace" class="mb-2">
                    <span class="flex items-center">
                      <i class="el-icon-refresh text-blue-500 mr-2"></i>
                      替换为正常帧
                      <span class="text-xs text-gray-500 ml-2">用前一帧或后一帧替换异常帧</span>
                    </span>
                  </el-radio>
                  <el-radio label="alert">
                    <span class="flex items-center">
                      <i class="el-icon-bell text-yellow-500 mr-2"></i>
                      报警记录
                      <span class="text-xs text-gray-500 ml-2">记录异常但不修改视频</span>
                    </span>
                  </el-radio>
                </el-radio-group>
              </div>
            </div>

            <!-- 静态帧检测配置 -->
            <div class="config-block">
              <div class="config-block-header">
                <i class="el-icon-video-camera text-blue-500 mr-2"></i>
                <span class="text-lg font-semibold">静态帧检测</span>
                <el-tooltip content="检测视频中的静止帧并进行相应处理" placement="top">
                  <i class="el-icon-question text-gray-400 ml-2 cursor-help"></i>
                </el-tooltip>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 连续帧数阈值 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    连续帧数阈值
                    <span class="text-red-500">*</span>
                    <el-tooltip content="判断为静止帧的连续帧数，建议值：5-15帧" placement="top">
                      <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
                    </el-tooltip>
                  </label>
                  <div class="flex items-center space-x-4">
                    <el-slider
                      v-model="staticFrameConfig.consecutiveFrames"
                      :min="1"
                      :max="30"
                      :step="1"
                      :show-tooltip="true"
                      class="flex-1"
                      @change="onStaticFrameChange"
                    />
                    <el-input-number
                      v-model="staticFrameConfig.consecutiveFrames"
                      :min="1"
                      :max="30"
                      :step="1"
                      size="small"
                      class="w-20"
                      @change="onStaticFrameChange"
                    />
                    <span class="text-sm text-gray-500">帧</span>
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    建议值：5-15帧
                  </div>
                </div>

                <!-- 特征相似度阈值 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    特征相似度阈值
                    <span class="text-red-500">*</span>
                    <el-tooltip content="帧间相似度判定阈值，0-1之间，建议值：0.85-0.95" placement="top">
                      <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
                    </el-tooltip>
                  </label>
                  <div class="flex items-center space-x-4">
                    <el-slider
                      v-model="staticFrameConfig.similarity"
                      :min="0"
                      :max="1"
                      :step="0.01"
                      :show-tooltip="true"
                      class="flex-1"
                      @change="onStaticFrameChange"
                    />
                    <el-input-number
                      v-model="staticFrameConfig.similarity"
                      :min="0"
                      :max="1"
                      :step="0.01"
                      :precision="2"
                      size="small"
                      class="w-24"
                      @change="onStaticFrameChange"
                    />
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    建议值：0.85-0.95
                  </div>
                </div>
              </div>

              <!-- 处理策略 -->
              <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">
                  处理策略
                  <span class="text-red-500">*</span>
                </label>
                <el-radio-group v-model="staticFrameConfig.strategy" @change="onStaticFrameChange">
                  <el-radio label="delete" class="mb-2">
                    <span class="flex items-center">
                      <i class="el-icon-delete text-red-500 mr-2"></i>
                      自动删除
                      <span class="text-xs text-gray-500 ml-2">直接删除检测到的静态帧</span>
                    </span>
                  </el-radio>
                  <el-radio label="mark" class="mb-2">
                    <span class="flex items-center">
                      <i class="el-icon-warning text-yellow-500 mr-2"></i>
                      仅标记
                      <span class="text-xs text-gray-500 ml-2">标记静态帧但不删除</span>
                    </span>
                  </el-radio>
                  <el-radio label="compress">
                    <span class="flex items-center">
                      <i class="el-icon-document text-blue-500 mr-2"></i>
                      压缩处理
                      <span class="text-xs text-gray-500 ml-2">保留首帧，删除后续重复帧</span>
                    </span>
                  </el-radio>
                </el-radio-group>
              </div>
            </div>

            <!-- 萃取规则 - 自定义脚本处理 -->
            <div class="config-block">
              <div class="config-block-header">
                <i class="el-icon-document text-green-500 mr-2"></i>
                <span class="text-lg font-semibold">自定义脚本处理</span>
                <el-tooltip content="上传Python脚本实现自定义视频处理逻辑" placement="top">
                  <i class="el-icon-question text-gray-400 ml-2 cursor-help"></i>
                </el-tooltip>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 脚本上传区域 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    Python脚本上传
                    <el-tooltip content="支持上传.py文件，文件大小不超过10MB" placement="top">
                      <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
                    </el-tooltip>
                  </label>
                  <el-upload
                    ref="scriptUpload"
                    class="upload-script"
                    drag
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :before-upload="beforeScriptUpload"
                    :on-success="onScriptUploadSuccess"
                    :on-error="onScriptUploadError"
                    :file-list="scriptFileList"
                    :limit="1"
                    :on-exceed="onScriptExceed"
                    accept=".py"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      将Python文件拖到此处，或<em>点击上传</em>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      只能上传.py文件，且不超过10MB
                    </div>
                  </el-upload>

                  <!-- 已上传的脚本信息 -->
                  <div v-if="customScript.fileName" class="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <i class="el-icon-document text-green-500 mr-2"></i>
                        <span class="text-sm font-medium">{{ customScript.fileName }}</span>
                      </div>
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-delete"
                        class="text-red-500"
                        @click="removeScript"
                      >
                        删除
                      </el-button>
                    </div>
                    <div class="mt-2 text-xs text-gray-600">
                      上传时间：{{ customScript.uploadTime }}
                    </div>
                  </div>
                </div>

                <!-- 脚本配置 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    脚本配置
                  </label>

                  <!-- 功能说明 -->
                  <div class="mb-4">
                    <label class="block text-xs font-medium text-gray-600 mb-2">功能说明</label>
                    <el-input
                      v-model="customScript.description"
                      type="textarea"
                      :rows="3"
                      placeholder="请描述脚本的功能和用途..."
                      maxlength="200"
                      show-word-limit
                      @input="onCustomScriptChange"
                    />
                  </div>

                  <!-- 启用开关 -->
                  <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-gray-700">启用脚本</label>
                    <el-switch
                      v-model="customScript.enabled"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                      @change="onCustomScriptChange"
                    />
                  </div>
                </div>
              </div>

              <!-- 脚本模板和文档 -->
              <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                  <h4 class="text-sm font-semibold text-gray-700">脚本接口模板</h4>
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-download"
                    @click="downloadTemplate"
                  >
                    下载模板
                  </el-button>
                </div>
                <div class="bg-gray-800 text-green-400 p-4 rounded text-sm font-mono overflow-x-auto">
                  <pre>{{ scriptTemplate }}</pre>
                </div>
                <div class="mt-3 text-xs text-gray-600">
                  <p><strong>使用说明：</strong></p>
                  <ul class="list-disc list-inside mt-1 space-y-1">
                    <li>脚本必须包含 <code class="bg-gray-200 px-1 rounded">process_frame(frame, metadata)</code> 函数</li>
                    <li>函数接收视频帧和元数据，返回处理后的帧或None（删除帧）</li>
                    <li>支持OpenCV、NumPy等常用图像处理库</li>
                    <li>脚本执行超时时间为30秒</li>
                  </ul>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 质量检测规则标签页 -->
          <el-tab-pane label="质量检测规则" name="quality">
            <!-- 时长异常检测配置 -->
            <div class="config-block mt-1">
              <div class="config-block-header">
                <i class="el-icon-time text-blue-500 mr-2"></i>
                <span class="text-lg font-semibold">时长异常检测</span>
                <el-tooltip content="检测视频时长是否符合预期范围" placement="top">
                  <i class="el-icon-question text-gray-400 ml-2 cursor-help"></i>
                </el-tooltip>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 检测类型和阈值 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    检测条件
                    <el-tooltip content="设置视频时长的检测条件" placement="top">
                      <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
                    </el-tooltip>
                  </label>
                  <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">视频时长</span>
                    <el-select v-model="durationConfig.type" placeholder="选择条件" @change="onDurationChange">
                      <el-option label="大于" value="greater"></el-option>
                      <el-option label="小于" value="less"></el-option>
                    </el-select>
                    <el-input-number
                      v-model="durationConfig.threshold"
                      :min="1"
                      :max="86400"
                      :step="1"
                      size="small"
                      class="w-32"
                      @change="onDurationChange"
                    />
                    <span class="text-sm text-gray-500">秒</span>
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    当视频时长{{ durationConfig.type === 'greater' ? '大于' : '小于' }}设定值时判定为异常
                  </div>
                </div>

                <!-- 启用开关 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    启用检测
                  </label>
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">时长异常检测</span>
                    <el-switch
                      v-model="durationConfig.enabled"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                      @change="onDurationChange"
                    />
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    启用后将自动检测视频时长异常
                  </div>
                </div>
              </div>
            </div>

            <!-- 解码参数检测配置 -->
            <div class="config-block">
              <div class="config-block-header">
                <i class="el-icon-video-play text-red-500 mr-2"></i>
                <span class="text-lg font-semibold">解码参数检测</span>
                <el-tooltip content="检测视频解码过程中的各种异常情况" placement="top">
                  <i class="el-icon-question text-gray-400 ml-2 cursor-help"></i>
                </el-tooltip>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 检测选项 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    检测项目
                    <span class="text-red-500">*</span>
                    <el-tooltip content="选择需要检测的解码异常类型" placement="top">
                      <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
                    </el-tooltip>
                  </label>
                  <div class="space-y-3">
                    <el-checkbox
                      v-model="decodingConfig.checkUnparseable"
                      class="w-full"
                      @change="onDecodingChange"
                    >
                      <span class="flex items-center">
                        <i class="el-icon-warning text-red-500 mr-2"></i>
                        无法解析视频文件
                        <span class="text-xs text-gray-500 ml-2">格式不支持或文件损坏</span>
                      </span>
                    </el-checkbox>
                    <el-checkbox
                      v-model="decodingConfig.checkDecodeErrors"
                      class="w-full"
                      @change="onDecodingChange"
                    >
                      <span class="flex items-center">
                        <i class="el-icon-picture text-orange-500 mr-2"></i>
                        解码过程异常
                        <span class="text-xs text-gray-500 ml-2">画面花屏、卡顿、绿屏、条纹或音频失真、无声</span>
                      </span>
                    </el-checkbox>
                    <el-checkbox
                      v-model="decodingConfig.checkDecodeInterruption"
                      class="w-full"
                      @change="onDecodingChange"
                    >
                      <span class="flex items-center">
                        <i class="el-icon-video-pause text-yellow-500 mr-2"></i>
                        解码进度中断
                        <span class="text-xs text-gray-500 ml-2">播放到某一帧突然停止</span>
                      </span>
                    </el-checkbox>
                  </div>
                </div>

                <!-- 启用开关和说明 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    启用检测
                  </label>
                  <div class="flex items-center justify-between mb-4">
                    <span class="text-sm text-gray-600">解码参数检测</span>
                    <el-switch
                      v-model="decodingConfig.enabled"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                      @change="onDecodingChange"
                    />
                  </div>
                  <div class="p-3 bg-blue-50 border border-blue-200 rounded">
                    <div class="text-sm font-medium text-blue-800 mb-2">检测说明</div>
                    <ul class="text-xs text-blue-700 space-y-1">
                      <li>检测将在视频处理过程中自动进行</li>
                      <li>发现异常时会在日志中记录详细信息</li>
                      <li>可根据检测结果决定是否继续处理</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <!-- 质量检测 - 自定义脚本处理 -->
            <div class="config-block mt-1">
              <div class="config-block-header">
                <i class="el-icon-document text-purple-500 mr-2"></i>
                <span class="text-lg font-semibold">自定义脚本处理</span>
                <el-tooltip content="上传Python脚本实现自定义视频质量检测逻辑" placement="top">
                  <i class="el-icon-question text-gray-400 ml-2 cursor-help"></i>
                </el-tooltip>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 脚本上传区域 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    Python脚本上传
                    <el-tooltip content="支持上传.py文件，文件大小不超过10MB" placement="top">
                      <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
                    </el-tooltip>
                  </label>
                  <el-upload
                    ref="qualityScriptUpload"
                    class="upload-script"
                    drag
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :before-upload="beforeQualityScriptUpload"
                    :on-success="onQualityScriptUploadSuccess"
                    :on-error="onQualityScriptUploadError"
                    :file-list="qualityScriptFileList"
                    :limit="1"
                    :on-exceed="onQualityScriptExceed"
                    accept=".py"
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      将Python文件拖到此处，或<em>点击上传</em>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      只能上传.py文件，且不超过10MB
                    </div>
                  </el-upload>

                  <!-- 已上传的脚本信息 -->
                  <div v-if="qualityScript.fileName" class="mt-4 p-3 bg-purple-50 border border-purple-200 rounded">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <i class="el-icon-document text-purple-500 mr-2"></i>
                        <span class="text-sm font-medium">{{ qualityScript.fileName }}</span>
                      </div>
                      <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-delete"
                        class="text-red-500"
                        @click="removeQualityScript"
                      >
                        删除
                      </el-button>
                    </div>
                    <div class="mt-2 text-xs text-gray-600">
                      上传时间：{{ qualityScript.uploadTime }}
                    </div>
                  </div>
                </div>

                <!-- 脚本配置 -->
                <div class="config-item">
                  <label class="block text-sm font-medium text-gray-700 mb-3">
                    脚本配置
                  </label>

                  <!-- 功能说明 -->
                  <div class="mb-4">
                    <label class="block text-xs font-medium text-gray-600 mb-2">功能说明</label>
                    <el-input
                      v-model="qualityScript.description"
                      type="textarea"
                      :rows="3"
                      placeholder="请描述脚本的功能和用途..."
                      maxlength="200"
                      show-word-limit
                      @input="onQualityScriptChange"
                    />
                  </div>

                  <!-- 启用开关 -->
                  <div class="flex items-center justify-between">
                    <label class="text-sm font-medium text-gray-700">启用脚本</label>
                    <el-switch
                      v-model="qualityScript.enabled"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                      @change="onQualityScriptChange"
                    />
                  </div>
                </div>
              </div>

              <!-- 脚本模板和文档 -->
              <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                  <h4 class="text-sm font-semibold text-gray-700">质量检测脚本接口模板</h4>
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-download"
                    @click="downloadQualityTemplate"
                  >
                    下载模板
                  </el-button>
                </div>
                <div class="bg-gray-800 text-green-400 p-4 rounded text-sm font-mono overflow-x-auto">
                  <pre>{{ qualityScriptTemplate }}</pre>
                </div>
                <div class="mt-3 text-xs text-gray-600">
                  <p><strong>使用说明：</strong></p>
                  <ul class="list-disc list-inside mt-1 space-y-1">
                    <li>脚本必须包含 <code class="bg-gray-200 px-1 rounded">check_quality(frame, metadata)</code> 函数</li>
                    <li>函数接收视频帧和元数据，返回质量评分和检测结果</li>
                    <li>支持OpenCV、NumPy等常用图像处理库</li>
                    <li>脚本执行超时时间为30秒</li>
                  </ul>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 配置预览对话框 -->
    <el-dialog
      title="配置预览"
      :visible.sync="previewDialogVisible"
      width="60%"
      :before-close="closePreviewDialog"
    >
      <div class="config-preview">
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">{{ configPreviewText }}</pre>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closePreviewDialog">关闭</el-button>
        <el-button type="primary" @click="copyConfig">复制配置</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { addDemo, listDemo, updateDemo } from '@/api/demo/index.js'
import { getToken } from '@/utils/auth'

export default {
  name: 'VideoCollectionConfig',
  data() {
    return {
      // 当前活动标签页
      activeTab: 'extraction',

      // 静态帧检测配置
      staticFrameConfig: {
        consecutiveFrames: 10, // 连续帧数阈值
        similarity: 0.90, // 特征相似度阈值
        strategy: 'mark' // 处理策略：delete, mark, compress
      },

      // 黑屏/花屏检测配置
      abnormalConfig: {
        blackScreenThreshold: 20, // 黑屏判定阈值
        noiseThreshold: 0.5, // 花屏判定阈值
        minDuration: 2.0, // 最小异常持续时间（秒）
        strategy: 'alert' // 处理策略：delete, replace, alert
      },

      // 自定义脚本配置（萃取规则）
      customScript: {
        fileName: '',
        filePath: '',
        description: '',
        enabled: false,
        uploadTime: ''
      },

      // 质量检测脚本配置
      qualityScript: {
        fileName: '',
        filePath: '',
        description: '',
        enabled: false,
        uploadTime: ''
      },

      // 时长异常检测配置
      durationConfig: {
        enabled: false,
        type: 'greater', // 'greater' 或 'less'
        threshold: 60 // 秒
      },

      // 解码参数检测配置
      decodingConfig: {
        enabled: false,
        checkUnparseable: false, // 无法解析视频文件
        checkDecodeErrors: false, // 解码过程异常
        checkDecodeInterruption: false // 解码进度中断
      },

      // 上传相关
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadMinio',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      scriptFileList: [],
      qualityScriptFileList: [],

      // 保存选项
      saveAsDefault: false,
      saving: false,

      // 预览对话框
      previewDialogVisible: false,

      // 脚本模板
      scriptTemplate: `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频处理自定义脚本模板
"""

import cv2
import numpy as np

def process_frame(frame, metadata):
    """
    处理单个视频帧

    Args:
        frame: OpenCV格式的视频帧 (numpy.ndarray)
        metadata: 帧元数据字典，包含：
            - frame_index: 帧索引
            - timestamp: 时间戳
            - width: 帧宽度
            - height: 帧高度
            - fps: 帧率

    Returns:
        numpy.ndarray: 处理后的帧，或None表示删除该帧
    """

    # 示例：简单的亮度调整
    # 可以根据需要实现自定义处理逻辑

    # 获取帧信息
    height, width = frame.shape[:2]

    # 示例处理：增加亮度
    processed_frame = cv2.convertScaleAbs(frame, alpha=1.1, beta=10)

    return processed_frame

# 可选：初始化函数
def initialize():
    """
    脚本初始化函数（可选）
    在处理开始前调用一次
    """
    pass

# 可选：清理函数
def cleanup():
    """
    脚本清理函数（可选）
    在处理结束后调用一次
    """
    pass`,

      // 质量检测脚本模板
      qualityScriptTemplate: `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频质量检测自定义脚本模板
"""

import cv2
import numpy as np

def check_quality(frame, metadata):
    """
    检测单个视频帧的质量

    Args:
        frame: OpenCV格式的视频帧 (numpy.ndarray)
        metadata: 帧元数据字典，包含：
            - frame_index: 帧索引
            - timestamp: 时间戳
            - width: 帧宽度
            - height: 帧高度
            - fps: 帧率

    Returns:
        dict: 质量检测结果，包含：
            - score: 质量评分 (0-100)
            - issues: 检测到的问题列表
            - details: 详细信息
    """

    # 示例：简单的质量评估
    # 可以根据需要实现自定义质量检测逻辑

    # 获取帧信息
    height, width = frame.shape[:2]

    # 示例检测：计算图像清晰度
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()

    # 示例检测：计算亮度
    brightness = np.mean(gray)

    # 示例检测：计算对比度
    contrast = gray.std()

    # 计算综合质量评分
    score = min(100, max(0, (laplacian_var / 100) * 50 + (contrast / 50) * 30 + (brightness / 255) * 20))

    # 检测问题
    issues = []
    if laplacian_var < 50:
        issues.append("图像模糊")
    if brightness < 50:
        issues.append("亮度过低")
    if brightness > 200:
        issues.append("亮度过高")
    if contrast < 20:
        issues.append("对比度过低")

    return {
        "score": round(score, 2),
        "issues": issues,
        "details": {
            "sharpness": round(laplacian_var, 2),
            "brightness": round(brightness, 2),
            "contrast": round(contrast, 2)
        }
    }

# 可选：初始化函数
def initialize():
    """
    脚本初始化函数（可选）
    在检测开始前调用一次
    """
    pass

# 可选：清理函数
def cleanup():
    """
    脚本清理函数（可选）
    在检测结束后调用一次
    """
    pass`
    }
  },

  computed: {
    // 配置预览文本
    configPreviewText() {
      return JSON.stringify({
        staticFrame: this.staticFrameConfig,
        abnormal: this.abnormalConfig,
        customScript: {
          ...this.customScript,
          filePath: this.customScript.filePath ? '***已上传***' : ''
        },
        qualityScript: {
          ...this.qualityScript,
          filePath: this.qualityScript.filePath ? '***已上传***' : ''
        },
        durationDetection: this.durationConfig,
        decodingDetection: this.decodingConfig,
        saveAsDefault: this.saveAsDefault
      }, null, 2)
    }
  },

  mounted() {
    this.loadConfig()
  },

  methods: {
    // 加载配置
    async loadConfig() {
      try {
        const response = await listDemo({
          type: 'video_param_setting'
        })
        if (response.code === 200) {
          const config = response.rows[0] || {}
          this.configId = config.id
          this.applyConfig(config)
        }

        // 临时使用默认配置
        console.log('配置加载完成')
      } catch (error) {
        this.$message.error('加载配置失败：' + error.message)
      }
    },

    // 应用配置
    applyConfig(config) {
      if (config.value1) {
        Object.assign(this.staticFrameConfig, JSON.parse(config.value1))
      }

      if (config.value2) {
        Object.assign(this.abnormalConfig, JSON.parse(config.value2))
      }

      if (config.value3) {
        Object.assign(this.customScript, JSON.parse(config.value3))
      }

      if (config.value5) {
        Object.assign(this.qualityScript, JSON.parse(config.value5))
      }

      if (config.value6) {
        const qualityDetectionConfig = JSON.parse(config.value6)
        if (qualityDetectionConfig.duration) {
          Object.assign(this.durationConfig, qualityDetectionConfig.duration)
        }
        if (qualityDetectionConfig.decoding) {
          Object.assign(this.decodingConfig, qualityDetectionConfig.decoding)
        }
      }

      if (typeof config.saveAsDefault === 'boolean') {
        this.saveAsDefault = config.value4 === '1'
      }
    },

    // 静态帧配置变化
    onStaticFrameChange() {
      this.validateStaticFrameConfig()
    },

    // 异常检测配置变化
    onAbnormalChange() {
      this.validateAbnormalConfig()
    },

    // 自定义脚本配置变化
    onCustomScriptChange() {
      // 可以在这里添加验证逻辑
    },

    // 质量检测脚本配置变化
    onQualityScriptChange() {
      // 可以在这里添加验证逻辑
    },

    // 时长异常检测配置变化
    onDurationChange() {
      this.validateDurationConfig()
    },

    // 解码参数检测配置变化
    onDecodingChange() {
      this.validateDecodingConfig()
    },

    // 保存选项变化
    onSaveOptionChange() {
      // 可以在这里添加相关逻辑
    },

    // 验证静态帧配置
    validateStaticFrameConfig() {
      const { consecutiveFrames, similarity } = this.staticFrameConfig

      if (consecutiveFrames < 1 || consecutiveFrames > 30) {
        this.$message.warning('连续帧数阈值应在1-30之间')
        return false
      }

      if (similarity < 0 || similarity > 1) {
        this.$message.warning('特征相似度阈值应在0-1之间')
        return false
      }

      return true
    },

    // 验证异常检测配置
    validateAbnormalConfig() {
      const { blackScreenThreshold, noiseThreshold, minDuration } = this.abnormalConfig

      if (blackScreenThreshold < 0 || blackScreenThreshold > 255) {
        this.$message.warning('黑屏判定阈值应在0-255之间')
        return false
      }

      if (noiseThreshold < 0 || noiseThreshold > 1) {
        this.$message.warning('花屏判定阈值应在0-1之间')
        return false
      }

      if (minDuration < 0.1 || minDuration > 10) {
        this.$message.warning('最小异常持续时间应在0.1-10秒之间')
        return false
      }

      return true
    },

    // 验证时长异常检测配置
    validateDurationConfig() {
      const { threshold } = this.durationConfig

      if (threshold < 1 || threshold > 86400) {
        this.$message.warning('时长阈值应在1-86400秒之间')
        return false
      }

      return true
    },

    // 验证解码参数检测配置
    validateDecodingConfig() {
      const { enabled, checkUnparseable, checkDecodeErrors, checkDecodeInterruption } = this.decodingConfig

      if (enabled && !checkUnparseable && !checkDecodeErrors && !checkDecodeInterruption) {
        this.$message.warning('启用解码参数检测时，至少需要选择一个检测项目')
        return false
      }

      return true
    },

    // 脚本上传前验证
    beforeScriptUpload(file) {
      const isValidType = file.type === 'text/x-python' || file.name.endsWith('.py')
      const isValidSize = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        this.$message.error('只能上传Python脚本文件(.py)!')
        return false
      }

      if (!isValidSize) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }

      return true
    },

    // 脚本上传成功
    onScriptUploadSuccess(response, file) {
      if (response.code === 200) {
        this.customScript.fileName = file.name
        this.customScript.filePath = response.url || response.data?.url
        this.customScript.uploadTime = new Date().toLocaleString()
        this.scriptFileList = [file]
        this.$message.success('脚本上传成功')
      } else {
        this.$message.error('脚本上传失败：' + (response.msg || '未知错误'))
      }
    },

    // 脚本上传失败
    onScriptUploadError(error) {
      this.$message.error('脚本上传失败：' + error.message)
    },

    // 脚本上传数量超限
    onScriptExceed() {
      this.$message.warning('只能上传一个脚本文件，请先删除已有文件')
    },

    // 删除脚本
    removeScript() {
      this.$confirm('确定要删除已上传的脚本吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.customScript.fileName = ''
        this.customScript.filePath = ''
        this.customScript.uploadTime = ''
        this.customScript.description = ''
        this.customScript.enabled = false
        this.scriptFileList = []
        this.$refs.scriptUpload.clearFiles()
        this.$message.success('脚本删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 下载脚本模板
    downloadTemplate() {
      const blob = new Blob([this.scriptTemplate], { type: 'text/plain;charset=utf-8' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'video_process_template.py'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      this.$message.success('模板下载成功')
    },

    // 质量检测脚本上传前验证
    beforeQualityScriptUpload(file) {
      const isValidType = file.type === 'text/x-python' || file.name.endsWith('.py')
      const isValidSize = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        this.$message.error('只能上传Python脚本文件(.py)!')
        return false
      }

      if (!isValidSize) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }

      return true
    },

    // 质量检测脚本上传成功
    onQualityScriptUploadSuccess(response, file) {
      if (response.code === 200) {
        this.qualityScript.fileName = file.name
        this.qualityScript.filePath = response.url || response.data?.url
        this.qualityScript.uploadTime = new Date().toLocaleString()
        this.qualityScriptFileList = [file]
        this.$message.success('质量检测脚本上传成功')
      } else {
        this.$message.error('质量检测脚本上传失败：' + (response.msg || '未知错误'))
      }
    },

    // 质量检测脚本上传失败
    onQualityScriptUploadError(error) {
      this.$message.error('质量检测脚本上传失败：' + error.message)
    },

    // 质量检测脚本上传数量超限
    onQualityScriptExceed() {
      this.$message.warning('只能上传一个质量检测脚本文件，请先删除已有文件')
    },

    // 删除质量检测脚本
    removeQualityScript() {
      this.$confirm('确定要删除已上传的质量检测脚本吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.qualityScript.fileName = ''
        this.qualityScript.filePath = ''
        this.qualityScript.uploadTime = ''
        this.qualityScript.description = ''
        this.qualityScript.enabled = false
        this.qualityScriptFileList = []
        this.$refs.qualityScriptUpload.clearFiles()
        this.$message.success('质量检测脚本删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 下载质量检测脚本模板
    downloadQualityTemplate() {
      const blob = new Blob([this.qualityScriptTemplate], { type: 'text/plain;charset=utf-8' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'video_quality_check_template.py'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      this.$message.success('质量检测模板下载成功')
    },

    // 重置配置
    resetConfig() {
      this.$confirm('确定要重置所有配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置为默认值
        this.staticFrameConfig = {
          consecutiveFrames: 10,
          similarity: 0.90,
          strategy: 'mark'
        }

        this.abnormalConfig = {
          blackScreenThreshold: 20,
          noiseThreshold: 0.5,
          minDuration: 2.0,
          strategy: 'alert'
        }

        this.customScript = {
          fileName: '',
          filePath: '',
          description: '',
          enabled: false,
          uploadTime: ''
        }

        this.qualityScript = {
          fileName: '',
          filePath: '',
          description: '',
          enabled: false,
          uploadTime: ''
        }

        this.durationConfig = {
          enabled: false,
          type: 'greater',
          threshold: 60
        }

        this.decodingConfig = {
          enabled: false,
          checkUnparseable: false,
          checkDecodeErrors: false,
          checkDecodeInterruption: false
        }

        this.saveAsDefault = false
        this.scriptFileList = []
        this.qualityScriptFileList = []
        this.activeTab = 'extraction'
        this.$refs.scriptUpload.clearFiles()
        if (this.$refs.qualityScriptUpload) {
          this.$refs.qualityScriptUpload.clearFiles()
        }

        this.$message.success('配置重置成功')
      }).catch(() => {
        // 用户取消重置
      })
    },

    // 预览配置
    previewConfig() {
      this.previewDialogVisible = true
    },

    // 关闭预览对话框
    closePreviewDialog() {
      this.previewDialogVisible = false
    },

    // 复制配置
    async copyConfig() {
      try {
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(this.configPreviewText)
        } else {
          // 降级方案
          const textArea = document.createElement('textarea')
          textArea.value = this.configPreviewText
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }
        this.$message.success('配置已复制到剪贴板')
      } catch (error) {
        this.$message.error('复制失败：' + error.message)
      }
    },

    // 保存配置
    async saveConfig() {
      // 验证配置
      if (!this.validateStaticFrameConfig() || !this.validateAbnormalConfig() ||
          !this.validateDurationConfig() || !this.validateDecodingConfig()) {
        return
      }

      // 如果启用了自定义脚本但没有上传文件
      if (this.customScript.enabled && !this.customScript.filePath) {
        this.$message.warning('请先上传萃取规则Python脚本文件')
        return
      }

      // 如果启用了质量检测脚本但没有上传文件
      if (this.qualityScript.enabled && !this.qualityScript.filePath) {
        this.$message.warning('请先上传质量检测Python脚本文件')
        return
      }

      this.saving = true

      try {
        const configData = {
          type: 'video_param_setting',
          value1: JSON.stringify(this.staticFrameConfig),
          value2: JSON.stringify(this.abnormalConfig),
          value3: JSON.stringify(this.customScript),
          value4: this.saveAsDefault ? '1' : '0',
          value5: JSON.stringify(this.qualityScript),
          value6: JSON.stringify({
            duration: this.durationConfig,
            decoding: this.decodingConfig
          }),
          remark: '视频异常检测和质量检测配置'
        }

        const handleApi = this.configId ? (params) => updateDemo({ ...params, id: this.configId }) : addDemo

        // 调用保存接口
        const response = await handleApi(configData)

        if (response.code === 200) {
          this.$message.success('配置保存成功')

          // 如果保存为默认配置，可以在这里添加额外逻辑
          if (this.saveAsDefault) {
            this.$message.info('已设置为默认配置')
          }
        } else {
          this.$message.error('配置保存失败：' + (response.msg || '未知错误'))
        }
      } catch (error) {
        this.$message.error('配置保存失败：' + error.message)
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.config-container {
}

.config-item {
  transition: all 0.3s ease;
}

.config-item:hover {
  transform: translateY(-1px);
}

/* 滑块样式优化 */
.el-slider {
  margin: 8px 0;
}

.el-slider__runway {
  background-color: #e4e7ed;
  border-radius: 3px;
}

.el-slider__bar {
  background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
  border-radius: 3px;
}

.el-slider__button {
  border: 2px solid #409eff;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

/* 单选框样式优化 */
.el-radio {
  margin-right: 0;
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.el-radio__label {
  padding-left: 8px;
  flex: 1;
}

/* 上传区域样式 */
.upload-script .el-upload {
  width: 100%;
}

.upload-script .el-upload-dragger {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.upload-script .el-upload-dragger:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.upload-script .el-upload-dragger .el-icon-upload {
  font-size: 64px;
  color: #c0c4cc;
}

.upload-script .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.upload-script .el-upload__text em {
  color: #409eff;
  font-style: normal;
}

/* 代码块样式 */
pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 卡片阴影效果 */
.shadow-el {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
  transition: box-shadow 0.3s ease;
}

.shadow-el:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-container {
    padding: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .flex {
    flex-direction: column;
    align-items: stretch;
  }

  .flex > * {
    margin-bottom: 0.5rem;
  }
}

/* 工具提示样式 */
.cursor-help {
  cursor: help;
}

/* 配置预览样式 */
.config-preview pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  font-size: 13px;
  line-height: 1.4;
  color: #495057;
}

/* 动画效果 */
.el-card {
  transition: all 0.3s ease;
}

.el-button {
  transition: all 0.2s ease;
}

.el-button:hover {
  transform: translateY(-1px);
}

/* 表单验证样式 */
.el-form-item.is-error .el-input__inner,
.el-form-item.is-error .el-textarea__inner {
  border-color: #f56c6c;
}

/* 成功状态样式 */
.bg-green-50 {
  background-color: #f0f9ff;
}

.border-green-200 {
  border-color: #a7f3d0;
}

.text-green-500 {
  color: #10b981;
}

/* 质量检测样式 */
.bg-purple-50 {
  background-color: #faf5ff;
}

.border-purple-200 {
  border-color: #e9d5ff;
}

.text-purple-500 {
  color: #8b5cf6;
}

/* 标签页样式优化 */
.config-tabs {
  margin-bottom: 0;
}

.config-tabs .el-tabs__header {
  margin: 0 0 24px 0;
  background: #fafbfc;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 6px;
  border: 1px solid #e8eaed;
}

.config-tabs .el-tabs__nav {
  border: none;
}

.config-tabs .el-tabs__item {
  border: 1px solid transparent;
  border-radius: 6px;
  margin-right: 8px;
  padding: 0 20px;
  height: 40px;
  line-height: 38px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.config-tabs .el-tabs__item:hover {
  background-color: #f8fafc;
  color: #409eff;
}

.config-tabs .el-tabs__item.is-active {
  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);
  color: #fff;
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.config-tabs .el-tabs__active-bar {
  display: none;
}

.config-tabs .el-tabs__content {
  padding: 0;
}

/* 配置块样式 */
.config-block {
  background: #fff;
  border: 1px solid #e8eaed;
  border-radius: 10px;
  margin-bottom: 20px;
  padding: 24px;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.config-block:hover {
  border-color: #c7d2fe;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.config-block:last-child {
  margin-bottom: 0;
}

.config-block-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f3f4;
  position: relative;
}

.config-block-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #409eff 0%, #36cfc9 100%);
  border-radius: 1px;
}

.config-block-header .text-lg {
  font-size: 17px;
  font-weight: 600;
  color: #1f2937;
  letter-spacing: -0.01em;
}

.config-block-header i:first-child {
  font-size: 20px;
  margin-right: 8px;
}

/* 紧凑布局调整 */
.config-tabs .el-tab-pane {
  padding: 0;
}

.config-item {
  margin-bottom: 20px;
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-item label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 12px;
}

/* 网格间距调整 */
.config-block .grid {
  gap: 20px;
}

/* 处理策略区域样式调整 */
.config-block .el-radio-group {
  margin-top: 12px;
}

.config-block .el-radio {
  margin-bottom: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.config-block .el-radio:hover {
  background-color: #f8fafc;
}

/* 脚本模板区域样式调整 */
.config-block .bg-gray-50 {
  margin-top: 20px;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

/* 输入组件样式优化 */
.config-block .el-input-number {
  border-radius: 6px;
}

.config-block .el-slider__runway {
  border-radius: 3px;
  background-color: #f1f3f4;
}

.config-block .el-slider__bar {
  border-radius: 3px;
}

.config-block .el-switch {
  margin-left: auto;
}

/* 上传组件样式优化 */
.config-block .upload-script .el-upload-dragger {
  border-radius: 8px;
  border: 2px dashed #d1d5db;
  background-color: #fafbfc;
  transition: all 0.3s ease;
}

.config-block .upload-script .el-upload-dragger:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .config-block {
    padding: 18px;
    margin-bottom: 16px;
  }

  .config-block-header {
    margin-bottom: 18px;
    padding-bottom: 12px;
  }

  .config-block-header .text-lg {
    font-size: 16px;
  }

  .config-block .grid {
    gap: 16px;
  }

  .config-item {
    margin-bottom: 16px;
  }
}
</style>
