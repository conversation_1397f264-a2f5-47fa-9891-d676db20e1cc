<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after>
      <!-- <el-button type="primary" size="small" @click="handleCreateTask">创建新任务</el-button> -->
    </template>

    <!-- 状态列自定义渲染 -->
    <!-- <template #table:value10:simple="{ row }">
      <el-tag :type="getStatusTagType(row.value10)">
        {{ row.value10 }}
      </el-tag>
    </template> -->

    <!-- 采集协议列自定义渲染 -->
    <template #table:value4:simple="{ row }">
      <el-tag type="info">
        {{ row.value4 }}
      </el-tag>
    </template>

    <template #table:value20:simple="{ row }">
      <el-switch v-model="row.value20" active-value="是" inactive-value="否" @change="(value) => handleDistribute(value, row)"></el-switch>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button
        v-if="['待执行', '已停止'].includes(row.value10)"
        type="text"
        size="mini"
        @click="handleExecute(row)"
      >
        执行
      </el-button>
      <el-button
        v-if="['运行中'].includes(row.value10)"
        type="text"
        size="mini"
        @click="handleStop(row)"
      >
        停止
      </el-button>
      <el-button
        v-if="['异常', '已完成'].includes(row.value10)"
        type="text"
        size="mini"
        @click="handleReExecute(row)"
      >
        重新执行
      </el-button>
    </template>

    <template #form:value25:simple="{ model }">
      <CronInput v-model="model.value25" />
    </template>

    <template #info:after="{model}">
      <div class="flex items-center mt-4 pb-4">
        <EleTitle content-position="left">采集过程日志</EleTitle>

        <el-button type="default" class="!ml-auto">复制日志</el-button>
        <el-button type="default">导出日志</el-button>
      </div>

      <div class="min-h-96 border p-2" v-html="model.value1"></div>

    </template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { taskStatus, collectionSourceType, collectionProtocol, videoCodecFormat, resolutionOptions, scheduleStrategy } from '@/dicts/video/index.js'
import CronInput from '@/components/CronInput/index.vue'
import dayjs from 'dayjs'
export default {
  name: 'VideoCollectionTaskManagement',
  components: {
    CronInput
  },
  data() {
    return {
      tableType: 'video_collection_task'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '采集任务',
        tableActionProps: {
          width: 320
        },
        api: {
          list: (params) => {
            // 处理搜索参数，确保字段映射正确
            const processedParams = this.handleSearchParams(params)
            return request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...processedParams,
                type: this.tableType
              }
            })
          },
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 基础字段映射 - 根据最新字段映射关系
          value1: {
            type: 'text',
            label: '任务编号',
            align: 'left',
            width: 120,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          value2: {
            type: 'text',
            label: '任务名称',
            align: 'left',
            width: 200,
            search: {
              placeholder: '请输入任务名称'
            },
            form: {
              rules: true
            }
          },
          // value3: {
          //   type: 'text',
          //   label: '任务编号',
          //   width: 150,
          //   search: {
          //     hidden: true,
          //     placeholder: '请输入任务编号'
          //   }
          // },
          value4: {
            type: 'select',
            label: '采集协议',
            width: 120,
            search: {
              hidden: true,
              type: 'select',
              options: [
                { label: '全部协议', value: '' },
                ...collectionProtocol
              ]
            },
            form: {
              value: collectionProtocol[0].value,
              fieldProps: {
                on: {
                  change: (value, label, ctx) => {
                    if (['FTP', 'SFTP'].includes(value)) {
                      ctx.model.value30 = 'root'
                      ctx.model.value31 = '123456'
                      ctx.model.value32 = '/data'
                    }

                    switch (value) {
                      case 'SFTP':
                        ctx.model.value5 = 'sftp://192.168.0.1:45'
                        break
                      // case 'RTMP':
                      //   ctx.model.value5 = 'rtmp://192.168.0.60:1935/JX801392?user=admin&pass=admin123'
                      //   break
                      case 'RTSP':
                        ctx.model.value5 = 'rtsp://admin:admin123@192.168.0.60:8554/stream'
                        break
                      case 'RTSPS':
                        ctx.model.value5 = 'rtsps://admin:admin123@192.168.0.60:8554/stream'
                        break
                      default:
                        ctx.model.value5 = ''
                        break
                    }
                  }
                }
              }
            },
            options: collectionProtocol
          },
          value30: {
            type: 'text',
            label: '用户名',
            width: 120,
            search: {
              hidden: true
            },
            form: {
              hidden: (model) => {
                return !['RTSPS', 'RTMP', 'FTP', 'SFTP'].includes(model.value4)
              }
              // rules: true
            },
            table: {
              hidden: true
            }
          },
          value31: {
            type: 'text',
            label: '密码',
            width: 120,
            search: {
              hidden: true
            },
            form: {
              hidden: (model) => {
                return !['RTSPS', 'RTMP', 'FTP', 'SFTP'].includes(model.value4)
              }
              // rules: true
            },
            table: {
              hidden: true
            }
          },
          value32: {
            type: 'text',
            label: '采集路径',
            width: 200,
            search: {
              hidden: true
            },
            form: {
              hidden: (model) => {
                return !['RTSPS', 'RTMP', 'FTP', 'SFTP'].includes(model.value4)
              }
              // rules: true
            },
            table: {
              hidden: true
            }
          },
          value5: {
            type: 'text',
            label: '第三方数据源',
            width: 200,
            search: {
              hidden: true
            },
            form: {
              sort: 25
              // value: 'rtmp://192.168.0.60:1935/JX801392?user=admin&pass=admin123'
            }
          },
          value6: {
            type: 'text',
            label: '视频源描述',
            width: 200,
            search: {
              hidden: true
            },
            form: {
              sort: 999,
              type: 'textarea'
            }
          },
          value7: {
            type: 'text',
            label: '采集开始时间',
            width: 160,
            search: {
              hidden: true,
              type: 'date-time-range',
              placeholder: '选择采集开始时间范围'
            },
            form: {
              hidden: true
            }
          },
          value8: {
            type: 'text',
            label: '采集结束时间',
            width: 160,
            search: {
              hidden: true,
              type: 'date-time-range',
              placeholder: '选择采集结束时间范围'
            },
            form: {
              hidden: true
            }
          },
          value9: {
            type: 'select',
            label: '采集帧率',
            width: 100,
            search: {
              hidden: true
            },
            form: {
              value: '18',
              sort: 800
            },
            options: [
              {
                label: '18',
                value: '18'
              },
              {
                label: '24',
                value: '24'
              },
              {
                label: '30',
                value: '30'
              },
              {
                label: '60',
                value: '60'
              }
            ]
          },
          value10: {
            type: 'select',
            label: '状态',
            width: 100,
            search: {
              hidden: true // 使用专用搜索字段 searchTaskStatus
            },
            table: {
              tableColumnProps: {
                fixed: 'right'
              }
            },
            form: {
              value: '待执行'
            },
            options: taskStatus
          },
          value11: {
            type: 'select',
            label: '调度策略',
            width: 120,
            search: {
              hidden: true,
              type: 'select',
              options: [
                ...scheduleStrategy
              ]
            },
            form: {
              value: scheduleStrategy[0].value
            },
            options: scheduleStrategy
          },
          value25: {
            label: 'Cron 表达式',
            width: 120,
            hidden: true,
            form: {
              hidden: (model) => {
                return model.value11 !== 'Cron表达式'
              }
            }
          },
          value12: {
            type: 'text',
            label: '异常重试',
            width: 100,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          value13: {
            type: 'text',
            label: '任务失败原因',
            width: 200,
            search: {
              hidden: true,
              placeholder: '请输入失败原因关键词'
            },
            form: {
              hidden: true
            }
          },
          value14: {
            type: 'select',
            label: '分辨率',
            width: 120,
            search: {
              hidden: true,
              type: 'select',
              options: [
                { label: '全部分辨率', value: '' },
                ...resolutionOptions
              ]
            },
            form: {
              value: resolutionOptions[0].value,
              sort: 850
            },
            options: resolutionOptions
          },
          value15: {
            type: 'select',
            label: '视频编码格式',
            width: 140,
            search: {
              hidden: true,
              type: 'select',
              options: [
                { label: '全部格式', value: '' },
                ...videoCodecFormat
              ]
            },
            form: {
              value: videoCodecFormat[0].value
            },
            options: videoCodecFormat
          },
          // value16: {
          //   type: 'text',
          //   label: '采集日志ID',
          //   width: 120,
          //   search: {
          //     hidden: true,
          //     placeholder: '请输入日志ID'
          //   },
          //   form: {
          //     hidden: true
          //   }
          // },
          value17: {
            type: 'text',
            label: '创建时间',
            width: 160,
            search: {
              hidden: true,
              type: 'date-time-range',
              placeholder: '选择创建时间范围'
            },
            form: {
              hidden: true
            },
            add: { value: dayjs().format('MM-DD HH:mm:ss') }
          },
          value18: {
            type: 'text',
            label: '最后更新时间',
            width: 160,
            search: {
              hidden: true,
              type: 'date-time-range',
              placeholder: '选择更新时间范围'
            },
            form: {
              hidden: true
            },
            add: { value: dayjs().format('MM-DD HH:mm:ss') }
          },
          value19: {
            type: 'text',
            label: '创建人',
            width: 120,
            search: {
              hidden: true,
              placeholder: '请输入创建人'
            },
            form: {
              hidden: true
            },
            add: { value: 'admin' }
          },
          value20: {
            type: 'text',
            label: '分发',
            width: 100,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            },
            tableColumnProps: {
              fixed: 'right'
            }
          },
          value26: {
            label: '是否质检',
            hidden: true,
            table: {
              hidden: false
            },
            form: {
              hidden: false,
              type: 'select',
              value: '是',
              options: [
                {
                  label: '是',
                  value: '是'
                },
                {
                  label: '否',
                  value: '否'
                }
              ]
            }
          },
          value27: {
            label: '是否萃取',
            hidden: true,
            table: {
              hidden: false
            },
            form: {
              hidden: false,
              type: 'select',
              value: '是',
              options: [
                {
                  label: '是',
                  value: '是'
                },
                {
                  label: '否',
                  value: '否'
                }
              ]
            }
          },
          value21: {
            label: '资产归属组织',
            hidden: true,
            form: {
              hidden: false,
              value: '北京机构'
            },
            options: [
              {
                label: '北京机构',
                value: '北京机构'
              },
              {
                label: '天津机构',
                value: '天津机构'
              }
            ]
          },
          value22: {
            label: '权限等级',
            hidden: true,
            form: {
              hidden: true,
              type: 'select',
              value: '二级(内部)',
              options: [
                {
                  label: '二级(内部)',
                  value: '二级(内部)'
                },
                {
                  label: '三级(保密)',
                  value: '三级(保密)'
                },
                {
                  label: '一级(公开)',
                  value: '一级(公开)'
                },
                {
                  label: '四级(机密)',
                  value: '四级(机密)'
                }
              ]
            }
          },
          value23: {
            label: '分级',
            hidden: true,
            form: {
              hidden: false,
              type: 'select',
              value: '一级(公开)',
              options: [
                {
                  label: '一级(公开)',
                  value: '一级(公开)'
                },
                {
                  label: '二级(内部)',
                  value: '二级(内部)'
                },
                {
                  label: '三级(保密)',
                  value: '三级(保密)'
                },
                {
                  label: '四级(机密)',
                  value: '四级(机密)'
                },
                {
                  label: '五级(绝密)',
                  value: '五级(绝密)'
                }
              ]
            }
          },
          value24: {
            label: '分类',
            hidden: true,
            form: {
              hidden: false,
              type: 'select',
              value: '新闻资讯',
              options: [
                {
                  label: '新闻资讯',
                  value: '新闻资讯'
                },
                {
                  label: '教育培训',
                  value: '教育培训'
                },
                {
                  label: '娱乐视频',
                  value: '娱乐视频'
                },
                {
                  label: '广告宣传',
                  value: '广告宣传'
                },
                {
                  label: '会议记录',
                  value: '会议记录'
                }
              ]
            }
          },

          // 专用搜索字段
          searchTaskStatus: {
            type: 'select',
            label: '任务状态',
            width: 120,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...taskStatus
              ],
              placeholder: '请选择任务状态'
            },
            table: {
              hidden: true
            },
            form: {
              hidden: true,
              value: '待执行'
            }
          },
          searchCollectionSource: {
            type: 'select',
            label: '采集源',
            width: 120,
            search: {
              type: 'select',
              options: [
                { label: '全部来源', value: '' },
                ...collectionSourceType
              ],
              placeholder: '请选择采集源'
            },
            table: {
              hidden: true
            },
            form: {
              hidden: true
            }
          }
        }
      }
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '运行中': 'success',
        '已完成': 'success',
        '已停止': 'info',
        '待执行': 'warning'
      }
      return statusMap[status] || 'info'
    },

    /**
     * 创建新任务
     * 打开新增任务对话框
     */
    handleCreateTask() {
      this.$refs.sheetRef.handleAdd()
    },

    /**
     * 分发任务
     * @param {Object} row - 任务行数据
     * @param {string} row.value1 - 任务编号
     * @param {string} row.value2 - 任务名称
     * @param {string} row.value3 - 任务编号
     * @param {string} row.value4 - 采集协议
     * @param {string} row.value5 - 第三方数据源
     * @param {string} row.value6 - 视频源描述
     * @param {string} row.value7 - 采集开始时间
     * @param {string} row.value8 - 采集结束时间
     * @param {string} row.value9 - 采集帧率
     * @param {string} row.value10 - 状态
     * @param {string} row.value11 - 调度策略
     * @param {string} row.value12 - 异常重试
     * @param {string} row.value13 - 任务失败原因
     * @param {string} row.value14 - 分辨率
     * @param {string} row.value15 - 视频编码格式
     * @param {string} row.value16 - 采集日志ID
     * @param {string} row.value17 - 创建时间
     * @param {string} row.value18 - 最后更新时间
     * @param {string} row.value19 - 创建人
     */
    handleDistribute(value, row) {
      this.sheetProps.api.edit({
        ...row,
        type: this.tableType,
        value20: value,
        action: '分发'
      }).then(() => {
        if (value === '是') {
          this.$alert(`${row.value40}`, '分发成功', {
            type: 'success',
            confirmButtonText: '确定'
          })
        }

        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(error => {
        this.$modal.msgError('分发失败：' + (error.message || '未知错误'))
      })
    },

    // 执行任务
    async handleExecute(row) {
      try {
        await this.$modal.confirm(`确认要执行当前任务吗？`)

        const updateData = {
          ...row,
          value10: '运行中',
          action: '执行',
          type: this.tableType
        }

        await request({
          url: '/system/AutoOsmotic',
          method: 'put',
          data: updateData
        })
        this.$modal.msgSuccess('任务已开始执行')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('执行任务失败:', error)
          this.$modal.msgError('执行任务失败')
        }
      }
    },

    // 停止任务
    handleStop(row) {
      this.$modal.confirm(`确认要停止当前任务吗？`).then(() => {
        // 通过修改 value10 字段来停止任务
        const updateData = {
          ...row,
          value10: '已停止',
          action: '停止',
          type: this.tableType
        }

        request({
          url: '/system/AutoOsmotic',
          method: 'put',
          data: updateData
        }).then(() => {
          this.$modal.msgSuccess('任务已停止')
          // 刷新列表
          this.$refs.sheetRef.getTableData()
        }).catch(error => {
          this.$modal.msgError('停止任务失败：' + (error.message || '未知错误'))
        })
      }).catch(() => {})
    },

    // 重新执行任务
    handleReExecute(row) {
      this.$modal.confirm(`确认要重新执行任务吗？`).then(() => {
        // 通过修改 value10 字段来重新执行任务
        const updateData = {
          ...row,
          value10: '运行中',
          action: '重新执行',
          type: this.tableType
        }

        request({
          url: '/system/AutoOsmotic',
          method: 'put',
          data: updateData
        }).then(() => {
          this.$modal.msgSuccess('任务已重新执行')
          // 刷新列表
          this.$refs.sheetRef.getTableData()
        }).catch(error => {
          this.$modal.msgError('重新执行任务失败：' + (error.message || '未知错误'))
        })
      }).catch(() => {})
    },

    /**
     * 处理搜索参数
     * 将搜索专用字段映射到对应的业务字段
     * @param {Object} params - 搜索参数
     * @returns {Object} 处理后的参数
     */
    handleSearchParams(params) {
      const processedParams = { ...params }

      // 任务状态搜索字段映射
      if (processedParams.searchTaskStatus) {
        processedParams.value10 = processedParams.searchTaskStatus
        delete processedParams.searchTaskStatus
      }

      // 采集源搜索字段映射
      if (processedParams.searchCollectionSource) {
        processedParams.collectionSource = processedParams.searchCollectionSource
        delete processedParams.searchCollectionSource
      }

      return processedParams
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
