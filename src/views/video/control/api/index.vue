<template>
  <div class="api-management">
    <!-- 统计图表区域 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <!-- API调用趋势图 -->
        <el-col :span="12">
          <el-card class="stats-card" size="mini">
            <template #header>
              <div class="card-header">
                <span class="card-title">API调用趋势</span>
                <div class="time-filter">
                  <el-radio-group v-model="timeFilter" size="mini" @change="getData">
                    <el-radio-button label="doDay">今日</el-radio-button>
                    <el-radio-button label="week">本周</el-radio-button>
                    <el-radio-button label="month">本月</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>
            <div ref="trendChart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- API调用动态雷达图 -->
        <el-col :span="12">
          <el-card class="stats-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">API成功率</span>
                <!-- <span class="success-rate">98.6%</span> -->
              </div>
            </template>
            <div ref="radarChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section page-main">
      <EleSheet ref="sheetRef" v-bind="sheetProps">
        <!-- 自定义认证方式列 -->
        <template #table:apiInfo:simple="{ row }">
          <div>{{ row.value1 }}</div>
          <div style="color: #999999;">{{ row.value2 }}</div>
          <div style="color: #999999;">版本：{{ row.value3 }}</div>
        </template>

        <!-- 自定义认证方式列 -->
        <template #table:value4:simple="{ row }">
          <el-tag :type="getAuthType(row.value4)" size="mini">
            {{ row.value4 }}
          </el-tag>
        </template>

        <!-- 自定义成功率列 -->
        <template #table:value7:simple="{ row }">
          <div class="success-rate-cell">
            <span class="rate-text">{{ row.value7 }}%</span>
            <el-progress :percentage="parseFloat(row.value7)" :color="getProgressColor(parseFloat(row.value7))"
              :show-text="false" :stroke-width="6" class="rate-progress" />
          </div>
        </template>

        <!-- 自定义状态列 -->
        <template #table:value8:simple="{ row }">
          <el-tag :type="getStatusType(row.value8)" size="mini">
            {{ row.value8 }}
          </el-tag>
        </template>

        <!-- 自定义操作列 -->
        <template #table:action:after="{ row }">
          <el-button type="text" size="mini" @click="handleAuthorize(row)">授权</el-button>
        </template>
      </EleSheet>
    </div>
    <Authorize ref="Authorize" @success="handleSuccess"></Authorize>
  </div>
</template>

<script>
import request from '@/utils/request.js'
import * as echarts from 'echarts'
import Authorize from './components/Authorize.vue'

export default {
  name: 'ApiManagement',
  data() {
    return {
      searchKeyword: '',
      timeFilter: 'doDay',
      trendChart: null,
      radarChart: null,
      tableType: 'api_data_openness_management',

      // 趋势图数据
      trendData: {
        times: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
        values: [1200, 1800, 2400, 6200, 5800, 4200, 3600, 2800]
      },

      // 雷达图数据
      radarData: {
        indicators: [
          { name: '响应速度', max: 100 },
          { name: '稳定性', max: 100 },
          { name: '安全性', max: 100 },
          { name: '可用性', max: 100 },
          { name: '兼容性', max: 100 }
        ],
        values: [85, 92, 88, 95, 78]
      }
    }
  },
  computed: {
    sheetProps() {
      return {
        title: 'API列表',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          edit: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },
        model: {
          apiInfo: {
            type: 'text',
            label: 'API信息',
            rules: true,
            search: { placeholder: '请输入API信息' },
            form: { hidden: true }
          },
          value1: {
            type: 'text',
            label: '接口名称',
            rules: true,
            search: { placeholder: '请输入接口名称' },
            table: { hidden: true }
          },
          value2: {
            type: 'text',
            label: '接口地址',
            rules: true,
            search: { placeholder: '请输入接口地址' },
            table: { hidden: true }
          },
          value3: {
            type: 'text',
            label: '版本',
            rules: true,
            search: { placeholder: '请输入版本' },
            table: { hidden: true }
          },
          value4: {
            type: 'select',
            label: '认证方式',
            rules: true,
            options: [
              { label: 'API密钥', value: 'API密钥' },
              { label: 'OAuth 2.0', value: 'OAuth 2.0' }
            ],
            search: { hidden: true }
          },
          value5: {
            type: 'text',
            label: '调用次数',
            search: { hidden: true },
            form: { hidden: true }
          },
          value9: {
            type: 'text',
            label: '授权租户',
            search: { hidden: true },
            form: { hidden: true }
          },
          value10: {
            type: 'text',
            label: '数据分类',
            search: { hidden: true },
            form: { hidden: true }
          },
          value11: {
            type: 'text',
            label: '数据分级',
            search: { hidden: true },
            form: { hidden: true }
          },
          value7: {
            type: 'text',
            label: '成功率',
            search: { hidden: true },
            form: { hidden: true }
          },
          value51: {
            type: 'text',
            label: '创建时间',
            search: { hidden: true },
            form: { hidden: true }
          },
          value8: {
            type: 'select',
            label: '状态',
            options: [
              { label: '上线', value: '上线' },
              { label: '下线', value: '下线' },
              { label: '禁用', value: '禁用' }
            ]
          }
        }
      }
    }
  },
  components: { Authorize },
  mounted() {
    this.$nextTick(() => {
      // this.initCharts()
      // this.initMockData()
      this.getData()
    })
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.trendChart) {
      this.trendChart.dispose()
    }
    if (this.radarChart) {
      this.radarChart.dispose()
    }
  },
  watch: {
    timeFilter() {
      this.updateTrendChart()
    }
  },
  methods: {
    async getData() {
      // api调用成功率
      const response1 = await request({
        url: '/system/AutoOsmotic/getApiCallingSuccessRate',
        method: 'GET',
        params: {
          timeType: this.timeFilter
        }
      })
      console.log(response1, 'response1')
      this.radarData.indicators = response1.data.map(item => ({name: item.name}))
      this.radarData.values = response1.data.map(item => item.callSuccessRate)

      // api调用趋势
      const response2 = await request({
        url: '/system/AutoOsmotic/getApiCallingTrends',
        method: 'GET',
        params: {
          timeType: this.timeFilter == 'doDay' ? 'toDay' : this.timeFilter
        }
      })
      console.log(response1, response2, "response2")

      this.trendData.times = response2.data.map(item => item.time)
      this.trendData.values = response2.data.map(item => item.callCount)
      this.initCharts()
    },

    // 初始化图表
    initCharts() {
      this.initTrendChart()
      this.initRadarChart()
    },

    // 初始化趋势图
    initTrendChart() {
      this.trendChart = echarts.init(this.$refs.trendChart)
      this.updateTrendChart()
    },

    // 更新趋势图
    updateTrendChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              // backgroundColor: '#6a7985'
            }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.trendData.times,
          axisLine: {
            lineStyle: {
              // color: '#e6ebf5'
            }
          },
          axisLabel: {
            // color: '#606266'
          }
        },
        yAxis: {
          type: 'value',
          name: '调用次数',
          axisLine: {
            lineStyle: {
              // color: '#e6ebf5'
            }
          },
          axisLabel: {
            // color: '#606266'
          },
          splitLine: {
            lineStyle: {
              color: '#f5f7fa'
            }
          }
        },
        series: [
          {
            name: '调用次数',
            type: 'line',
            smooth: true,
            data: this.trendData.values,
            lineStyle: {
              color: '#409EFF',
              width: 3
            },
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
                ]
              }
            }
          }
        ]
      }
      this.trendChart.setOption(option)
    },

    // 初始化雷达图
    initRadarChart() {
      this.radarChart = echarts.init(this.$refs.radarChart)

      const option = {
        tooltip: {
          trigger: 'item'
        },
        radar: {
          indicator: this.radarData.indicators,
          center: ['50%', '50%'],
          radius: '70%',
          axisLine: {
            lineStyle: {
              // color: '#e6ebf5'
            }
          },
          splitLine: {
            lineStyle: {
              // color: '#f5f7fa'
            }
          },
          splitArea: {
            show: false
          },
          axisLabel: {
            fontSize: 12
          }
        },
        series: [
          {
            name: 'API调用动态',
            type: 'radar',
            data: [
              {
                value: this.radarData.values,
                name: '当前状态',
                itemStyle: {
                  color: '#409EFF'
                },
                areaStyle: {
                  color: 'rgba(64, 158, 255, 0.2)'
                },
                lineStyle: {
                  color: '#409EFF',
                  width: 2
                }
              }
            ]
          }
        ]
      }
      this.radarChart.setOption(option)
    },

    // 窗口大小变化处理
    handleResize() {
      if (this.trendChart) {
        this.trendChart.resize()
      }
      if (this.radarChart) {
        this.radarChart.resize()
      }
    },

    // 打开授权弹框
    handleAuthorize(row) {
      this.$refs.Authorize.handleOpen(row)
    },
    handleSuccess() {
       this.$refs.sheetRef.getTableData()
    },

    // 搜索处理
    handleSearch() {
      if (this.searchKeyword.trim()) {
        this.$refs.sheetRef.searchMixin.model.value1 = this.searchKeyword
        this.$refs.sheetRef.getTableData()
      }
    },

    // 新增API
    handleAddApi() {
      this.$refs.sheetRef.handleAdd()
    },

    // 查看详情
    handleView(row) {
      this.$refs.sheetRef.handleInfo(row)
    },

    // 编辑
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },

    // 配置
    handleConfig(row) {
      this.$message.info(`配置 ${row.value1} API 功能开发中...`)
    },

    // 获取认证方式标签类型
    getAuthType(auth) {
      const authMap = {
        'API密钥': 'primary',
        'OAuth 2.0': 'success'
      }
      return authMap[auth] || 'info'
    },

    // 获取状态标签类型
    getStatusType(status) {
      const statusMap = {
        '已启用': 'success',
        '已禁用': 'danger',
        '上线': 'success',
        '下线': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 获取进度条颜色
    getProgressColor(percentage) {
      if (percentage >= 95) return '#67C23A'
      if (percentage >= 85) return '#E6A23C'
      return '#F56C6C'
    },

    // 初始化模拟数据
    async initMockData() {
      try {
        // 检查是否已有数据
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: this.tableType,
            pageNum: 1,
            pageSize: 1
          }
        })

        // 如果没有数据，则添加模拟数据
        if (!response.rows || response.rows.length === 0) {
          await this.addMockApiData()
        }
      } catch (error) {
        console.log('初始化模拟数据失败:', error)
      }
    },

    // 添加模拟API数据
    async addMockApiData() {
      const mockApis = [
        {
          value1: '视频内容查重API',
          value2: 'API密钥',
          value3: '125846',
          value4: '99.2',
          value5: '2023-01-10 14:30:00',
          value6: '已启用',
          type: this.tableType
        },
        {
          value1: '视频流媒体API',
          value2: 'OAuth 2.0',
          value3: '86421',
          value4: '98.7',
          value5: '2023-01-15 09:15:00',
          value6: '已启用',
          type: this.tableType
        },
        {
          value1: '智能上传API',
          value2: 'API密钥',
          value3: '24563',
          value4: '97.5',
          value5: '2023-02-20 16:45:00',
          value6: '已启用',
          type: this.tableType
        },
        {
          value1: '视频内容审核API',
          value2: 'OAuth 2.0',
          value3: '8752',
          value4: '95.3',
          value5: '2023-03-05 11:20:00',
          value6: '已启用',
          type: this.tableType
        },
        {
          value1: '租户权限管理API',
          value2: 'API密钥',
          value3: '3254',
          value4: '99.1',
          value5: '2023-04-10 13:55:00',
          value6: '已禁用',
          type: this.tableType
        }
      ]

      // 批量添加模拟数据
      for (const apiData of mockApis) {
        try {
          await request({
            url: '/system/AutoOsmotic',
            method: 'post',
            data: apiData
          })
        } catch (error) {
          console.log('添加模拟数据失败:', error)
        }
      }

      console.log('API管理模拟数据初始化完成')
    }
  }
}
</script>

<style scoped>
.api-management {
  min-height: 100vh;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 280px;
}

/* 统计图表区域 */
.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  height: 360px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.success-rate {
  font-size: 18px;
  font-weight: 600;
  color: #67C23A;
}

.time-filter {
  margin-left: auto;
}

.chart-container {
  height: 280px;
  width: 100%;
}

/* 表格区域 */
.table-section {}

/* 自定义表格列样式 */
.success-rate-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rate-text {
  font-weight: 600;
  min-width: 40px;
}

.rate-progress {
  flex: 1;
  max-width: 80px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .search-input {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .api-management {
    padding: 12px;
  }

  .chart-container {
    height: 240px;
  }

  .header-right {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }
}
</style>
